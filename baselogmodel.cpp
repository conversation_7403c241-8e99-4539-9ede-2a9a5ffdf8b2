#include "baselogmodel.h"
#include <QColor>
#include <QBrush>
#include <QFont>
#include <QDateTime>
#include <QDebug>

BaseLogModel::BaseLogModel(QObject* parent)
    : QAbstractTableModel(parent)
{
    initializeColumnVisibility();
}

int BaseLogModel::columnCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent)
    return static_cast<int>(ColumnCount);
}

QVariant BaseLogModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole) {
        return getColumnName(section);
    }
    return QVariant();
}

void BaseLogModel::setColumnVisible(Column column, bool visible)
{
    if (column >= 0 && column < ColumnCount) {
        QMutexLocker locker(&m_mutex);
        m_columnVisibility[static_cast<int>(column)] = visible;
    }
}

bool BaseLogModel::isColumnVisible(Column column) const
{
    if (column >= 0 && column < ColumnCount) {
        QMutexLocker locker(&m_mutex);
        return m_columnVisibility[static_cast<int>(column)];
    }
    return false;
}

QVector<bool> BaseLogModel::getColumnVisibility() const
{
    QMutexLocker locker(&m_mutex);
    return m_columnVisibility;
}

void BaseLogModel::setColumnVisibility(const QVector<bool>& visibility)
{
    QMutexLocker locker(&m_mutex);
    if (visibility.size() == static_cast<int>(ColumnCount)) {
        m_columnVisibility = visibility;
    }
}

int BaseLogModel::mapToActualColumn(int visibleColumn) const
{
    QMutexLocker locker(&m_mutex);
    int actualColumn = 0;
    int visibleCount = 0;
    
    for (int i = 0; i < static_cast<int>(ColumnCount); ++i) {
        if (m_columnVisibility[i]) {
            if (visibleCount == visibleColumn) {
                actualColumn = i;
                break;
            }
            visibleCount++;
        }
    }
    
    return actualColumn;
}

QString BaseLogModel::getColumnName(int column) const
{
    switch (column) {
        case TimestampColumn:
            return tr("时间戳");
        case LevelColumn:
            return tr("级别");
        case SourceColumn:
            return tr("来源");
        case MessageColumn:
            return tr("消息");
        case DetailsColumn:
            return tr("详情");
        default:
            return tr("未知");
    }
}

QVariant BaseLogModel::formatLogEntryData(const LogEntry& entry, int column, int role) const
{

    // 处理不同的角色
    switch (role)
    {
        case Qt::DisplayRole:
            switch (column)
            {
                case TimestampColumn:
                    return entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz");
                case LevelColumn:
                    return entry.levelString();
                case SourceColumn:
                    return entry.source();
                case MessageColumn:
                    return entry.message();
                case DetailsColumn:
                    return entry.details();
                default:
                    return QVariant();
            }

        case Qt::BackgroundRole:
            switch (entry.level())
            {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(240, 240, 240));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(255, 255, 255));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(255, 255, 200));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(255, 200, 200));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(255, 150, 150));
                default:
                    return QVariant();
            }

        case Qt::ForegroundRole:
            switch (entry.level())
            {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(100, 100, 100));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(0, 0, 0));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(150, 100, 0));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(200, 0, 0));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(150, 0, 0));
                default:
                    return QVariant();
            }

        case Qt::FontRole: {
            QFont font;
            if (entry.level() == LogEntry::LogLevel::Critical || entry.level() == LogEntry::LogLevel::Error)
            {
                font.setBold(true);
            }
            return font;
        }

        case Qt::TextAlignmentRole:
            switch (column)
            {
                case TimestampColumn:
                case LevelColumn:
                    return Qt::AlignCenter;
                default:
                    return QVariant(Qt::AlignLeft | Qt::AlignVCenter);
            }

        default:
            return QVariant();
    }

}

void BaseLogModel::initializeColumnVisibility()
{
    m_columnVisibility.resize(static_cast<int>(ColumnCount));
    for (int i = 0; i < static_cast<int>(ColumnCount); ++i) {
        m_columnVisibility[i] = true; // 默认所有列可见
    }
}
