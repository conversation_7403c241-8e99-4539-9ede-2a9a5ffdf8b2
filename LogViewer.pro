QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
CONFIG += console

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

TARGET = SimpleLogViewer
TEMPLATE = app

# 定义宏
DEFINES += SIMPLE_VERSION
DEFINES += LOGVIEWER_STATIC_BUILD
DEFINES += LOGVIEWER_LIBRARY_SRC

# 包含路径
INCLUDEPATH += src
INCLUDEPATH += ../include  # Log4Qt头文件路径

# 库路径
LIBS += -L../bin  # Log4Qt库路径

# 编译器设置
win32 {
    # Windows特定设置
    # 移除有问题的编译器标志
    # QMAKE_CXXFLAGS += "/utf-8"

    # 启用Log4Qt支持
    DEFINES += LOG4QT_AVAILABLE
    LIBS += -llog4qt
}

HEADERS += \
    asyncfilereader_worker.h \
    logviewer_global.h \
    logentry.h \
    logmodel.h \
    baselogmodel.h \
    circularlogmodel.h \
    filelogmodel.h \
    logmodelfactory.h \
    logsortfilterproxymodel.h \
    idatasource.h \
    logger.h \
    simplelogviewer.h \
    simpleconfigmanager.h \
    simplefiledatasource.h \
    streamlined_log4qt_source.h \
    streamlined_log_appender.h \
    filterexpression.h \
    compositefilterexpression.h \
    circularlogbuffer.h \
    simplelog4qtdatasource.h \
    simplelogviewerappender.h \
    simple_test.h


SOURCES += \
    asyncfilereader_worker.cpp \
    main.cpp \
    simple_test.cpp \
    logentry.cpp \
    logmodel.cpp \
    baselogmodel.cpp \
    circularlogmodel.cpp \
    filelogmodel.cpp \
    logmodelfactory.cpp \
    logsortfilterproxymodel.cpp \
    logger.cpp \
    simplelogviewer.cpp \
    simpleconfigmanager.cpp \
    simplefiledatasource.cpp \
    streamlined_log4qt_source.cpp \
    streamlined_log_appender.cpp \
    filterexpression.cpp \
    compositefilterexpression.cpp \
    circularlogbuffer.cpp \
    simplelog4qtdatasource.cpp \
    simplelogviewerappender.cpp

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
