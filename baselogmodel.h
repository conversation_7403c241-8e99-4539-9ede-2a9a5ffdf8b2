#ifndef BASELOGMODEL_H
#define BASELOGMODEL_H

#include "logviewer_global.h"
#include "logentry.h"
#include <QAbstractTableModel>
#include <QVector>
#include <QMutex>

/**
 * @brief 日志Model的抽象基类
 * 
 * 提供公共的Model功能，减少代码重复
 * 子类需要实现具体的数据存储和管理策略
 */
class LOGVIEWER_EXPORT BaseLogModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    /**
     * @brief 列枚举
     */
    enum Column {
        TimestampColumn = 0,    ///< 时间戳列
        LevelColumn,            ///< 日志级别列
        SourceColumn,           ///< 来源列
        MessageColumn,          ///< 消息列
        DetailsColumn,          ///< 详情列
        ColumnCount             ///< 列总数
    };

    explicit BaseLogModel(QObject* parent = nullptr);
    virtual ~BaseLogModel() = default;

    // ========== QAbstractTableModel公共实现 ==========
    int columnCount(const QModelIndex& parent = QModelIndex()) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

    // ========== 列可见性管理 ==========
    
    /**
     * @brief 设置列可见性
     * @param column 列索引
     * @param visible 是否可见
     */
    void setColumnVisible(Column column, bool visible);
    
    /**
     * @brief 检查列是否可见
     * @param column 列索引
     * @return 是否可见
     */
    bool isColumnVisible(Column column) const;
    
    /**
     * @brief 获取列可见性配置
     * @return 列可见性向量
     */
    QVector<bool> getColumnVisibility() const;
    
    /**
     * @brief 设置列可见性配置
     * @param visibility 列可见性向量
     */
    void setColumnVisibility(const QVector<bool>& visibility);
    
    /**
     * @brief 将可见列索引映射到实际列索引
     * @param visibleColumn 可见列索引
     * @return 实际列索引
     */
    int mapToActualColumn(int visibleColumn) const;

    // ========== 纯虚函数，子类必须实现 ==========
    
    /**
     * @brief 添加单个日志条目
     * @param entry 日志条目
     */
    virtual void addLogEntry(const LogEntry& entry) = 0;
    
    /**
     * @brief 批量添加日志条目
     * @param entries 日志条目列表
     */
    virtual void addLogEntries(const QVector<LogEntry>& entries) = 0;
    
    /**
     * @brief 获取指定行的日志条目
     * @param row 行索引
     * @return 日志条目
     */
    virtual LogEntry getLogEntry(int row) const = 0;
    
    /**
     * @brief 获取指定范围的日志条目
     * @param startIndex 起始索引
     * @param count 数量
     * @return 日志条目列表
     */
    virtual QVector<LogEntry> getEntries(int startIndex, int count) const = 0;
    
    /**
     * @brief 清空所有数据
     */
    virtual void clear() = 0;
    
    /**
     * @brief 获取内存使用量（字节）
     * @return 内存使用量
     */
    virtual qint64 getMemoryUsage() const = 0;
    
    /**
     * @brief 获取总条目数
     * @return 总条目数
     */
    virtual int getTotalCount() const = 0;
    
    /**
     * @brief 获取Model类型标识
     * @return 类型字符串
     */
    virtual QString getModelType() const = 0;

signals:
    /**
     * @brief 内存警告信号
     */
    void memoryWarning();
    
    /**
     * @brief 数据变化信号
     */
    void dataChanged();

protected:
    QVector<bool> m_columnVisibility;   ///< 列可见性配置
    mutable QMutex m_mutex;             ///< 线程安全锁
    
    /**
     * @brief 获取列显示名称
     * @param column 列索引
     * @return 列名称
     */
    QString getColumnName(int column) const;
    
    /**
     * @brief 格式化日志条目数据用于显示
     * @param entry 日志条目
     * @param column 列索引
     * @param role 显示角色
     * @return 格式化后的数据
     */
    QVariant formatLogEntryData(const LogEntry& entry, int column, int role) const;
    
    /**
     * @brief 初始化列可见性
     */
    void initializeColumnVisibility();
};

#endif // BASELOGMODEL_H
